'use client'

import { useState, useEffect } from "react"
import { Calendar, Clock, MapPin, MoreHorizontal, Video, User, CheckCircle2, AlertCircle, Search, XCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { supabase } from '@/lib/supabase'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

interface Interview {
  id: string
  application_id: string
  bpo_user_id: string
  scheduled_at?: string
  duration_minutes: number
  status: 'scheduled' | 'completed' | 'cancelled' | 'pending_scheduling'
  notes?: string
  meeting_link?: string
  location?: string
  feedback?: any
  created_at: string
  updated_at: string
  // Enriched data
  bpoName?: string
  bpoLogo?: string
  position?: string
  isUpcoming?: boolean
  formattedDate?: string
  formattedTime?: string
}

export function InterviewsPage() {
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(null)
  const [showAcceptDialog, setShowAcceptDialog] = useState(false)
  const [prospectNotes, setProspectNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      console.log('🔍 SIMPLE: Starting fetchData...')

      // FOR TESTING: Just show some hardcoded data to verify the UI works
      const testInterviews = [
        {
          id: 'test-1',
          application_id: 'test-app-1',
          bpo_user_id: 'test-bpo-1',
          scheduled_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
          duration_minutes: 60,
          status: 'pending_scheduling',
          notes: 'Test interview invitation',
          meeting_link: 'https://meet.google.com/test',
          location: 'virtual',
          bpoName: 'Test BPO Company',
          bpoLogo: null,
          position: 'Customer Support Agent',
          formattedDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleDateString(),
          formattedTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          isUpcoming: true
        },
        {
          id: 'test-2',
          application_id: 'test-app-2',
          bpo_user_id: 'test-bpo-2',
          scheduled_at: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(), // Day after tomorrow
          duration_minutes: 45,
          status: 'scheduled',
          notes: 'Confirmed interview',
          meeting_link: 'https://meet.google.com/test2',
          location: 'virtual',
          bpoName: 'Another BPO Company',
          bpoLogo: null,
          position: 'Technical Support Specialist',
          formattedDate: new Date(Date.now() + 48 * 60 * 60 * 1000).toLocaleDateString(),
          formattedTime: new Date(Date.now() + 48 * 60 * 60 * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          isUpcoming: true
        }
      ]

      console.log('🔍 SIMPLE: Setting test interviews:', testInterviews)
      setInterviews(testInterviews)

    } catch (error) {
      console.error('🔍 SIMPLE: Error:', error)
      setInterviews([])
    } finally {
      setLoading(false)
    }
  }

  const handleAcceptInterview = async () => {
    if (!selectedInterview || !selectedTimeSlot) return

    try {
      setIsSubmitting(true)

      const response = await fetch('/api/interviews/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interviewId: selectedInterview.id,
          selectedTime: selectedTimeSlot,
          prospectNotes: prospectNotes
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to accept interview')
      }

      const selectedTime = new Date(selectedTimeSlot)
      toast({
        title: "Interview accepted!",
        description: `Your interview has been scheduled for ${selectedTime.toLocaleDateString()} at ${selectedTime.toLocaleTimeString()}.`,
      })

      setShowAcceptDialog(false)
      setSelectedInterview(null)
      setProspectNotes('')
      fetchData() // Refresh the list

    } catch (error: any) {
      console.error('Error accepting interview:', error)
      toast({
        title: "Failed to accept interview",
        description: error.message || "There was an error accepting your interview. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeclineInterview = async (interview: Interview, reason: string) => {
    try {
      const response = await fetch('/api/interviews/accept', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interviewId: interview.id,
          reason: reason
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to decline interview')
      }

      toast({
        title: "Interview declined",
        description: "The interview invitation has been declined.",
      })

      fetchData() // Refresh the list

    } catch (error: any) {
      console.error('Error declining interview:', error)
      toast({
        title: "Failed to decline interview",
        description: error.message || "There was an error declining the interview. Please try again.",
        variant: "destructive",
      })
    }
  }

  const openAcceptDialog = (interview: Interview) => {
    setSelectedInterview(interview)
    setShowAcceptDialog(true)
  }



  if (loading && interviews.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading interviews...</p>
        </div>
      </div>
    )
  }

  const filteredInterviews = getFilteredInterviews()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Interviews</h1>
        <p className="text-gray-600">Your interview invitations and scheduled interviews</p>
      </div>

      {/* Simple Interview List */}
      <div className="space-y-4">
        {interviews.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">No interviews yet</h3>
              <p className="text-gray-600 mb-4">
                When you apply for jobs and get selected, interview invitations will appear here.
              </p>
              <Button asChild>
                <a href="/prospect/job-board">Browse Jobs</a>
              </Button>
            </CardContent>
          </Card>
        ) : (
          interviews.map((interview) => (
            <Card key={interview.id}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold text-lg">{interview.position}</h3>
                    <p className="text-gray-600">{interview.bpoName}</p>
                    <div className="mt-2 space-y-1">
                      <p className="text-sm">
                        <Clock className="h-4 w-4 inline mr-1" />
                        {interview.formattedDate} at {interview.formattedTime}
                      </p>
                      <p className="text-sm">
                        <Video className="h-4 w-4 inline mr-1" />
                        {interview.location || 'Virtual'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={interview.status === 'pending_scheduling' ? 'outline' : 'default'}>
                      {interview.status}
                    </Badge>
                    {interview.status === 'pending_scheduling' && (
                      <div className="mt-2 space-x-2">
                        <Button size="sm" onClick={() => openAcceptDialog(interview)}>
                          Accept
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleDeclineInterview(interview, 'Not available')}>
                          Decline
                        </Button>
                      </div>
                    )}
                    {interview.meeting_link && interview.status === 'scheduled' && (
                      <div className="mt-2">
                        <Button size="sm" onClick={() => window.open(interview.meeting_link, '_blank')}>
                          Join Meeting
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>



      {/* Accept Interview Dialog */}
      <Dialog open={showAcceptDialog} onOpenChange={setShowAcceptDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Accept Interview</DialogTitle>
            <DialogDescription>
              Confirm that you want to accept this interview invitation.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Input
                id="notes"
                placeholder="Any questions or requirements..."
                value={prospectNotes}
                onChange={(e) => setProspectNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAcceptDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAcceptInterview} disabled={isSubmitting}>
              {isSubmitting ? "Accepting..." : "Accept"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
