'use client'

import { useState, useEffect } from "react"
import { Calendar, Clock, MapPin, MoreHorizontal, Video, User, CheckCircle2, AlertCircle, Search, XCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { supabase } from '@/lib/supabase'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

interface Interview {
  id: string
  application_id: string
  bpo_user_id: string
  scheduled_at?: string
  duration_minutes: number
  status: 'scheduled' | 'completed' | 'cancelled' | 'pending_scheduling'
  notes?: string
  meeting_link?: string
  location?: string
  feedback?: any
  created_at: string
  updated_at: string
  // Enriched data
  bpoName?: string
  bpoLogo?: string
  position?: string
  isUpcoming?: boolean
  formattedDate?: string
  formattedTime?: string
}

export function InterviewsPage() {
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [prospectData, setProspectData] = useState<any>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(null)
  const [showAcceptDialog, setShowAcceptDialog] = useState(false)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState('')
  const [prospectNotes, setProspectNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [interviewStats, setInterviewStats] = useState({
    total: 0,
    upcoming: 0,
    completed: 0,
    cancelled: 0,
    pending: 0
  })
  const { toast } = useToast()

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      // Get the current user
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return

      setUser(session.user)

      // Get the prospect profile for this user
      const { data: prospect, error: prospectError } = await supabase
        .from('prospects')
        .select('*')
        .eq('user_id', session.user.id)
        .single()

      if (prospectError || !prospect) {
        console.error('Error fetching prospect profile:', prospectError)
        return
      }

      setProspectData(prospect)

      try {
        // First, get all applications for this prospect
        console.log('Fetching applications for prospect:', prospect.id)

        const { data: applications, error: applicationsError } = await supabase
          .from('applications')
          .select('id')
          .eq('prospect_id', prospect.id)

        if (applicationsError) {
          console.error('Error fetching applications:', applicationsError)
          throw applicationsError
        }

        console.log('Found applications:', applications)

        // If no applications, return empty array
        if (!applications || applications.length === 0) {
          console.log('No applications found for prospect')
          setInterviews([])
          setInterviewStats({
            total: 0,
            upcoming: 0,
            completed: 0,
            cancelled: 0,
            pending: 0
          })
          return
        }

        const applicationIds = applications.map(app => app.id)
        console.log('Application IDs:', applicationIds)

        // Get all interviews for these applications
        const { data: interviewData, error: interviewError } = await supabase
          .from('interviews')
          .select('*')
          .in('application_id', applicationIds)

        if (interviewError) {
          console.error('Error fetching interviews:', interviewError)
          throw interviewError
        }

        console.log('Raw interview data:', interviewData)

        // If no interviews, return empty array
        if (!interviewData || interviewData.length === 0) {
          console.log('No interviews found')
          setInterviews([])
          setInterviewStats({
            total: 0,
            upcoming: 0,
            completed: 0,
            cancelled: 0,
            pending: 0
          })
          return
        }

        // Now enrich the interviews with additional data
        const enrichedInterviews = []

        for (const interview of interviewData) {
          try {
            let bpoName = 'Unknown Company'
            let bpoLogo = null
            let position = 'Unknown Position'

            console.log('Processing interview:', interview.id, 'with application_id:', interview.application_id)

            // Get application data to fetch BPO and position info
            const { data: applicationData, error: appError } = await supabase
              .from('applications')
              .select(`
                id,
                job_id,
                job_postings!inner(
                  id,
                  title,
                  bpo_id,
                  bpos!inner(
                    id,
                    name,
                    logo_url
                  )
                )
              `)
              .eq('id', interview.application_id)
              .single()

            if (!appError && applicationData) {
              console.log('Application data found:', applicationData)

              // Get BPO and position info
              bpoName = applicationData.job_postings?.bpos?.name || 'Unknown Company'
              bpoLogo = applicationData.job_postings?.bpos?.logo_url || null
              position = applicationData.job_postings?.title || 'Unknown Position'

              console.log('Final interview info:', { bpoName, position })
            } else {
              console.log('Error fetching application data:', appError)
            }

            enrichedInterviews.push({
              ...interview,
              bpoName,
              bpoLogo,
              position
            })

          } catch (enrichError) {
            console.error('Error enriching interview:', interview.id, enrichError)
            // Include the interview anyway with default values
            enrichedInterviews.push({
              ...interview,
              bpoName: 'Unknown Company',
              bpoLogo: null,
              position: 'Unknown Position'
            })
          }
        }

        console.log('Enriched interviews:', enrichedInterviews)

        // Process interview data with timing information
        const processedInterviews = enrichedInterviews.map(interview => {
          const now = new Date()
          const interviewDate = interview.scheduled_at ? new Date(interview.scheduled_at) : null

          // Determine if the interview is upcoming (in the future)
          const isUpcoming = interviewDate ? interviewDate > now : false

          return {
            ...interview,
            isUpcoming,
            formattedDate: interviewDate ? interviewDate.toLocaleDateString() : 'TBD',
            formattedTime: interviewDate ? interviewDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'TBD'
          }
        })

        setInterviews(processedInterviews)

        // Calculate statistics
        const stats = {
          total: processedInterviews.length,
          upcoming: processedInterviews.filter(i => i.isUpcoming && i.status === 'scheduled').length,
          completed: processedInterviews.filter(i => i.status === 'completed').length,
          cancelled: processedInterviews.filter(i => i.status === 'cancelled').length,
          pending: processedInterviews.filter(i => i.status === 'pending_scheduling').length
        }

        setInterviewStats(stats)

      } catch (interviewError) {
        console.error('Error processing interviews:', interviewError)
        setInterviews([])
        setInterviewStats({
          total: 0,
          upcoming: 0,
          completed: 0,
          cancelled: 0,
          pending: 0
        })
      }

    } catch (error) {
      console.error('Error fetching data:', error)
      setInterviews([])
      setInterviewStats({
        total: 0,
        upcoming: 0,
        completed: 0,
        cancelled: 0,
        pending: 0
      })
    } finally {
      setLoading(false)
    }
  }

  const handleAcceptInterview = async () => {
    if (!selectedInterview || !selectedTimeSlot) return

    try {
      setIsSubmitting(true)

      const response = await fetch('/api/interviews/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interviewId: selectedInterview.id,
          selectedTime: selectedTimeSlot,
          prospectNotes: prospectNotes
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to accept interview')
      }

      const selectedTime = new Date(selectedTimeSlot)
      toast({
        title: "Interview accepted!",
        description: `Your interview has been scheduled for ${selectedTime.toLocaleDateString()} at ${selectedTime.toLocaleTimeString()}.`,
      })

      setShowAcceptDialog(false)
      setSelectedInterview(null)
      setSelectedTimeSlot('')
      setProspectNotes('')
      fetchData() // Refresh the list

    } catch (error: any) {
      console.error('Error accepting interview:', error)
      toast({
        title: "Failed to accept interview",
        description: error.message || "There was an error accepting your interview. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeclineInterview = async (interview: Interview, reason: string) => {
    try {
      const response = await fetch('/api/interviews/accept', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interviewId: interview.id,
          reason: reason
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to decline interview')
      }

      toast({
        title: "Interview declined",
        description: "The interview invitation has been declined.",
      })

      fetchData() // Refresh the list

    } catch (error: any) {
      console.error('Error declining interview:', error)
      toast({
        title: "Failed to decline interview",
        description: error.message || "There was an error declining the interview. Please try again.",
        variant: "destructive",
      })
    }
  }

  const openAcceptDialog = (interview: Interview) => {
    setSelectedInterview(interview)
    setShowAcceptDialog(true)
  }

  const getFilteredInterviews = () => {
    return interviews.filter(interview => {
      // Apply status filter
      if (statusFilter !== 'all') {
        if (statusFilter === 'upcoming' && (!interview.isUpcoming || interview.status !== 'scheduled')) {
          return false
        } else if (statusFilter === 'pending' && interview.status !== 'pending_scheduling') {
          return false
        } else if (statusFilter !== 'upcoming' && statusFilter !== 'pending' && interview.status !== statusFilter) {
          return false
        }
      }

      // Apply search query
      if (searchQuery) {
        const searchLower = searchQuery.toLowerCase()
        return (
          (interview.bpoName && interview.bpoName.toLowerCase().includes(searchLower)) ||
          (interview.position && interview.position.toLowerCase().includes(searchLower))
        )
      }

      return true
    })
  }

  const getStatusBadge = (interview: Interview) => {
    // Check if interview needs response (pending scheduling)
    if (interview.status === 'pending_scheduling') {
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
          <AlertCircle className="h-3 w-3 mr-1" />
          Awaiting Response
        </Badge>
      )
    }

    switch (interview.status) {
      case 'scheduled':
        return interview.isUpcoming ? (
          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
            Upcoming
          </Badge>
        ) : (
          <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
            Scheduled (Past)
          </Badge>
        )
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        )
      case 'cancelled':
        return (
          <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
            <XCircle className="h-3 w-3 mr-1" />
            Cancelled
          </Badge>
        )
      default:
        return <Badge>{interview.status}</Badge>
    }
  }

  const getInitials = (name: string) => {
    if (!name || name === 'Unknown Company') return 'UC'

    return name.split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }



  if (loading && interviews.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading interviews...</p>
        </div>
      </div>
    )
  }

  const filteredInterviews = getFilteredInterviews()

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-8">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Interviews</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Manage your upcoming interviews and practice sessions
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Total Interviews
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {interviewStats.total}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                <Clock className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Upcoming
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {interviewStats.upcoming}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-amber-500 rounded-md p-3">
                <AlertCircle className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Pending Response
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {interviewStats.pending}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                <CheckCircle2 className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Completed
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {interviewStats.completed}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-red-500 rounded-md p-3">
                <XCircle className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Cancelled
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {interviewStats.cancelled}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500 dark:text-gray-400" />
          <Input
            placeholder="Search interviews..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant={statusFilter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('all')}
          >
            All
          </Button>
          <Button
            variant={statusFilter === 'pending' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('pending')}
          >
            Pending Response
          </Button>
          <Button
            variant={statusFilter === 'upcoming' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('upcoming')}
          >
            Upcoming
          </Button>
          <Button
            variant={statusFilter === 'completed' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('completed')}
          >
            Completed
          </Button>
          <Button
            variant={statusFilter === 'cancelled' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('cancelled')}
          >
            Cancelled
          </Button>
        </div>
      </div>

      {/* Interviews Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Company</TableHead>
                <TableHead>Position</TableHead>
                <TableHead>Date & Time</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Location</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInterviews.map((interview) => (
                <TableRow key={interview.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={interview.bpoLogo || undefined} alt={interview.bpoName} />
                        <AvatarFallback>{getInitials(interview.bpoName || 'Unknown')}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{interview.bpoName}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{interview.position}</TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium">{interview.formattedDate}</span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">{interview.formattedTime}</span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">{interview.duration_minutes} mins</span>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(interview)}</TableCell>
                  <TableCell>
                    {interview.location === 'virtual' ? (
                      <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/30 dark:text-purple-400 dark:border-purple-800">
                        <Video className="h-3 w-3 mr-1" />
                        Virtual
                      </Badge>
                    ) : (
                      interview.location || 'TBD'
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {interview.status === 'pending_scheduling' && (
                          <>
                            <DropdownMenuItem onClick={() => openAcceptDialog(interview)}>
                              <CheckCircle2 className="h-4 w-4 mr-2" />
                              <span>Accept Interview</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleDeclineInterview(interview, 'Not available')}
                            >
                              <XCircle className="h-4 w-4 mr-2" />
                              <span>Decline Interview</span>
                            </DropdownMenuItem>
                          </>
                        )}

                        {interview.meeting_link && interview.status === 'scheduled' && interview.isUpcoming && (
                          <DropdownMenuItem onClick={() => window.open(interview.meeting_link, '_blank')}>
                            <Video className="h-4 w-4 mr-2" />
                            <span>Join Meeting</span>
                          </DropdownMenuItem>
                        )}

                        <DropdownMenuItem>
                          <User className="h-4 w-4 mr-2" />
                          <span>View Details</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}

              {filteredInterviews.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center">
                      <Calendar className="h-8 w-8 mb-2 text-gray-400" />
                      <p>No interviews found</p>
                      <p className="text-sm mt-1">
                        {interviews.length > 0
                          ? 'Try changing your filters or search query'
                          : 'When you apply for jobs and get selected, interview invitations will appear here.'}
                      </p>

                      {interviews.length === 0 && (
                        <Button
                          variant="outline"
                          className="mt-4"
                          asChild
                        >
                          <a href="/prospect/job-board">
                            <Calendar className="h-4 w-4 mr-2" />
                            <span>Browse Jobs</span>
                          </a>
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>



      {/* Accept Interview Dialog */}
      <Dialog open={showAcceptDialog} onOpenChange={setShowAcceptDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-green-600" />
              Accept Interview Invitation
            </DialogTitle>
            <DialogDescription>
              {selectedInterview && (
                <>Select your preferred time for the interview with {selectedInterview.bpoName} for the {selectedInterview.position} position.</>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="text-sm text-muted-foreground p-3 bg-amber-50 rounded-lg border border-amber-200">
              The interviewer will contact you to arrange a suitable time after you accept this invitation.
            </div>

            <div className="space-y-2">
              <Label htmlFor="prospect-notes">Additional Notes (Optional)</Label>
              <Input
                id="prospect-notes"
                placeholder="Any questions or special requirements..."
                value={prospectNotes}
                onChange={(e) => setProspectNotes(e.target.value)}
              />
            </div>

            <div className="text-sm text-muted-foreground">
              Duration: {selectedInterview?.duration_minutes || 60} minutes
            </div>
          </div>

          <DialogFooter className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowAcceptDialog(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAcceptInterview}
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? "Accepting..." : "Accept Interview"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
