import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    console.log('🔍 DEBUG: Current user:', {
      id: session.user.id,
      email: session.user.email,
      role: session.user.user_metadata?.role
    })

    // Step 1: Check if user exists in users table
    const { data: userRecord, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', session.user.id)
      .single()

    console.log('🔍 DEBUG: User record:', userRecord, 'Error:', userError)

    // Step 2: Check if prospect profile exists
    const { data: prospect, error: prospectError } = await supabase
      .from('prospects')
      .select('*')
      .eq('user_id', session.user.id)
      .single()

    console.log('🔍 DEBUG: Prospect profile:', prospect, 'Error:', prospectError)

    if (prospectError || !prospect) {
      return NextResponse.json({
        debug: {
          user: userRecord,
          prospect: null,
          prospectError: prospectError?.message,
          applications: [],
          interviews: [],
          message: 'No prospect profile found'
        }
      })
    }

    // Step 3: Check applications for this prospect
    const { data: applications, error: applicationsError } = await supabase
      .from('applications')
      .select('*')
      .eq('prospect_id', prospect.id)

    console.log('🔍 DEBUG: Applications:', applications, 'Error:', applicationsError)

    // Step 3.1: Check applications with different query approaches
    const { data: allApplications, error: allAppsError } = await supabase
      .from('applications')
      .select('*')

    console.log('🔍 DEBUG: ALL Applications in database:', allApplications, 'Error:', allAppsError)

    // Step 3.2: Check applications using service role (bypass RLS)
    const supabaseService = createRouteHandlerClient({
      cookies: () => cookieStore,
      supabaseKey: process.env.SUPABASE_SERVICE_ROLE_KEY
    })

    const { data: serviceApplications, error: serviceAppsError } = await supabaseService
      .from('applications')
      .select('*')
      .eq('prospect_id', prospect.id)

    console.log('🔍 DEBUG: Applications (service role):', serviceApplications, 'Error:', serviceAppsError)

    // Step 4: Check all interviews in the database
    const { data: allInterviews, error: allInterviewsError } = await supabase
      .from('interviews')
      .select('*')

    console.log('🔍 DEBUG: All interviews in database:', allInterviews, 'Error:', allInterviewsError)

    // Step 4.1: Check interviews using service role
    const { data: serviceInterviews, error: serviceInterviewsError } = await supabaseService
      .from('interviews')
      .select('*')

    console.log('🔍 DEBUG: All interviews (service role):', serviceInterviews, 'Error:', serviceInterviewsError)

    // Step 5: If we have applications, check for interviews
    let interviewsForApplications = []
    if (serviceApplications && serviceApplications.length > 0) {
      const applicationIds = serviceApplications.map(app => app.id)

      const { data: interviews, error: interviewsError } = await supabaseService
        .from('interviews')
        .select('*')
        .in('application_id', applicationIds)

      console.log('🔍 DEBUG: Interviews for applications:', interviews, 'Error:', interviewsError)
      interviewsForApplications = interviews || []
    }

    // Step 6: Check job postings and BPOs for context
    const { data: jobPostings, error: jobError } = await supabase
      .from('job_postings')
      .select('id, title, bpo_id, bpos(name)')
      .limit(5)

    console.log('🔍 DEBUG: Sample job postings:', jobPostings, 'Error:', jobError)

    // Step 7: Check BPO teams to see if there are any BPO users
    const { data: bpoTeams, error: bpoTeamsError } = await supabase
      .from('bpo_teams')
      .select('user_id, bpo_id, bpos(name)')
      .limit(5)

    console.log('🔍 DEBUG: Sample BPO teams:', bpoTeams, 'Error:', bpoTeamsError)

    return NextResponse.json({
      debug: {
        currentUser: {
          id: session.user.id,
          email: session.user.email,
          role: session.user.user_metadata?.role
        },
        userRecord,
        prospect,
        applications: applications || [],
        allApplications: allApplications || [],
        serviceApplications: serviceApplications || [],
        interviewsForApplications,
        allInterviews: allInterviews || [],
        serviceInterviews: serviceInterviews || [],
        sampleJobPostings: jobPostings || [],
        sampleBpoTeams: bpoTeams || [],
        summary: {
          hasProspectProfile: !!prospect,
          applicationCount: applications?.length || 0,
          serviceApplicationCount: serviceApplications?.length || 0,
          allApplicationCount: allApplications?.length || 0,
          interviewCount: interviewsForApplications.length,
          totalInterviewsInDB: allInterviews?.length || 0,
          serviceInterviewsInDB: serviceInterviews?.length || 0
        }
      }
    })

  } catch (error) {
    console.error('🔍 DEBUG: Error in debug endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
